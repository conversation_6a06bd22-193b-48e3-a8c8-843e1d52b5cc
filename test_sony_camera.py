#!/usr/bin/env python3
"""
Script simple para probar la conexión con la cámara Sony ZV-E10
"""

import pysony
import time

def test_camera_connection():
    """Prueba básica de conexión con la cámara Sony"""
    
    print("🎥 Probando conexión con Sony ZV-E10...")
    print("=" * 50)
    
    try:
        # Crear instancia de la API
        print("1. Creando conexión con la cámara...")
        camera = pysony.SonyAPI()
        
        # Intentar obtener lista de APIs disponibles
        print("2. Obteniendo lista de APIs disponibles...")
        api_list = camera.getAvailableApiList()
        
        if api_list:
            print("✅ ¡Conexión exitosa!")
            print(f"📋 APIs disponibles ({len(api_list)}):")
            for i, api in enumerate(api_list, 1):
                print(f"   {i}. {api}")
            
            # Intentar tomar una foto
            print("\n3. Intentando tomar una fotografía...")
            try:
                result = camera.actTakePicture()
                if result:
                    print("✅ ¡Fotografía tomada exitosamente!")
                else:
                    print("⚠️  La foto no se pudo tomar (puede que la cámara no esté en modo correcto)")
            except Exception as e:
                print(f"⚠️  Error al tomar foto: {e}")
            
            return True
            
        else:
            print("❌ No se pudo obtener la lista de APIs")
            return False
            
    except Exception as e:
        print(f"❌ Error de conexión: {e}")
        print("\n📋 Pasos para configurar WiFi en Sony ZV-E10:")
        print("1. Enciende la cámara")
        print("2. Ve a Menu → Network → Wi-Fi Settings")
        print("3. Selecciona 'Ctrl w/ Smartphone'")
        print("4. Selecciona 'On This Device' para crear punto de acceso")
        print("5. Anota el nombre de red (SSID) y contraseña que aparecen")
        print("6. Conecta tu PC a esa red WiFi")
        print("7. Ejecuta este script nuevamente")
        print("\nAlternativamente:")
        print("- Puedes usar 'Access Point Set.' para conectar ambos a la misma red")
        return False

if __name__ == "__main__":
    success = test_camera_connection()
    
    if success:
        print("\n🎉 ¡Todo funcionando correctamente!")
        print("Ahora puedes usar 'sony_camera_control.py' para control completo")
    else:
        print("\n🔧 Revisa la configuración WiFi de tu cámara")
