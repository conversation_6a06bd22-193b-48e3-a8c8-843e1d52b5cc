#!/usr/bin/env python3
"""
Control Integrado: Impresora Bambu Labs + Cámara Sony ZV-E10
Permite controlar ambos dispositivos desde una sola interfaz
"""

import bambulabs_api as bl
import time
import json
from sony_camera_control import SonyCameraController

# Configuración de la impresora Bambu Labs
PRINTER_IP = '**************'  # Reemplaza con la IP de tu impresora
PRINTER_ACCESS_CODE = '40541866'  # Código de acceso
PRINTER_SERIAL = '03919C452109217'  # Número de serie

class IntegratedController:
    """Controlador integrado para impresora y cámara"""
    
    def __init__(self):
        self.printer = None
        self.camera = None
        self.printer_connected = False
        self.camera_connected = False
    
    def connect_printer(self):
        """Conecta con la impresora Bambu Labs"""
        try:
            print("🖨️  Conectando a impresora Bambu Labs...")
            self.printer = bl.Printer(PRINTER_IP, PRINTER_ACCESS_CODE, PRINTER_SERIAL)
            self.printer.mqtt_start()
            time.sleep(3)  # Esperar conexión MQTT
            
            # Verificar conexión
            status = self.printer.get_state()
            if status:
                self.printer_connected = True
                print("✅ Impresora conectada exitosamente")
                return True
            else:
                print("❌ No se pudo verificar el estado de la impresora")
                return False
                
        except Exception as e:
            print(f"❌ Error al conectar impresora: {e}")
            return False
    
    def connect_camera(self):
        """Conecta con la cámara Sony"""
        print("📷 Conectando a cámara Sony ZV-E10...")
        self.camera = SonyCameraController()
        self.camera_connected = self.camera.discover_camera()
        return self.camera_connected
    
    def take_timelapse_photo(self, filename_prefix="timelapse"):
        """Toma una foto con timestamp para timelapse"""
        if not self.camera_connected:
            print("❌ Cámara no conectada")
            return False
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        print(f"📸 Tomando foto de timelapse: {filename_prefix}_{timestamp}")
        return self.camera.take_photo()
    
    def monitor_print_with_photos(self, photo_interval=300):
        """
        Monitorea la impresión y toma fotos periódicamente
        photo_interval: intervalo en segundos entre fotos (default: 5 minutos)
        """
        if not self.printer_connected:
            print("❌ Impresora no conectada")
            return
        
        if not self.camera_connected:
            print("❌ Cámara no conectada")
            return
        
        print(f"🎬 Iniciando monitoreo con fotos cada {photo_interval} segundos")
        
        last_photo_time = 0
        
        try:
            while True:
                # Obtener estado de la impresora
                try:
                    printer_status = self.printer.get_state()
                    current_time = time.time()
                    
                    print(f"🖨️  Estado impresora: {printer_status}")
                    
                    # Tomar foto si ha pasado el intervalo
                    if current_time - last_photo_time >= photo_interval:
                        if self.take_timelapse_photo("print_progress"):
                            last_photo_time = current_time
                    
                    # Si la impresión terminó, salir
                    if printer_status and "finish" in str(printer_status).lower():
                        print("🎉 ¡Impresión completada!")
                        # Tomar foto final
                        self.take_timelapse_photo("print_finished")
                        break
                    
                except Exception as e:
                    print(f"⚠️  Error al obtener estado: {e}")
                
                # Esperar antes del siguiente chequeo
                time.sleep(30)  # Chequear cada 30 segundos
                
        except KeyboardInterrupt:
            print("\n⏹️  Monitoreo interrumpido por el usuario")
    
    def disconnect_all(self):
        """Desconecta todos los dispositivos"""
        if self.printer_connected and self.printer:
            print("🔌 Desconectando impresora...")
            self.printer.mqtt_stop()
            self.printer_connected = False
        
        if self.camera_connected and self.camera:
            self.camera.disconnect()
            self.camera_connected = False

def main():
    """Función principal con menú interactivo"""
    print("🎛️  Control Integrado: Impresora + Cámara")
    print("=" * 50)
    
    controller = IntegratedController()
    
    while True:
        print("\n" + "=" * 50)
        print("MENÚ PRINCIPAL")
        print("1. Conectar impresora Bambu Labs")
        print("2. Conectar cámara Sony ZV-E10")
        print("3. Tomar foto individual")
        print("4. Monitorear impresión con fotos automáticas")
        print("5. Control manual de impresora")
        print("6. Control manual de cámara")
        print("7. Estado de dispositivos")
        print("8. Salir")
        
        choice = input("\nSelecciona una opción (1-8): ").strip()
        
        if choice == '1':
            controller.connect_printer()
            
        elif choice == '2':
            controller.connect_camera()
            
        elif choice == '3':
            if controller.camera_connected:
                controller.take_timelapse_photo("manual_photo")
            else:
                print("❌ Conecta la cámara primero")
                
        elif choice == '4':
            if controller.printer_connected and controller.camera_connected:
                interval = input("Intervalo entre fotos en segundos (default 300): ").strip()
                try:
                    interval = int(interval) if interval else 300
                    controller.monitor_print_with_photos(interval)
                except ValueError:
                    print("❌ Intervalo inválido, usando 300 segundos")
                    controller.monitor_print_with_photos(300)
            else:
                print("❌ Conecta ambos dispositivos primero")
                
        elif choice == '5':
            if controller.printer_connected:
                print("\n🖨️  Control de Impresora:")
                print("a. Encender luz")
                print("b. Apagar luz")
                print("c. Ver estado detallado")
                
                sub_choice = input("Selecciona (a/b/c): ").strip().lower()
                
                if sub_choice == 'a':
                    controller.printer.turn_light_on()
                    print("💡 Luz encendida")
                elif sub_choice == 'b':
                    controller.printer.turn_light_off()
                    print("🔦 Luz apagada")
                elif sub_choice == 'c':
                    try:
                        status = controller.printer.mqtt_dump()
                        print(json.dumps(status, indent=2, sort_keys=True))
                    except Exception as e:
                        print(f"❌ Error: {e}")
            else:
                print("❌ Conecta la impresora primero")
                
        elif choice == '6':
            if controller.camera_connected:
                print("\n📷 Control de Cámara:")
                print("a. Tomar foto")
                print("b. Iniciar Live View")
                print("c. Detener Live View")
                print("d. Ver estado")
                
                sub_choice = input("Selecciona (a/b/c/d): ").strip().lower()
                
                if sub_choice == 'a':
                    controller.camera.take_photo()
                elif sub_choice == 'b':
                    url = controller.camera.start_liveview()
                    if url:
                        print(f"📹 Live View: {url}")
                elif sub_choice == 'c':
                    controller.camera.stop_liveview()
                elif sub_choice == 'd':
                    status = controller.camera.get_camera_status()
                    if status:
                        print(json.dumps(status, indent=2, default=str))
            else:
                print("❌ Conecta la cámara primero")
                
        elif choice == '7':
            print(f"\n📊 Estado de Dispositivos:")
            print(f"🖨️  Impresora: {'✅ Conectada' if controller.printer_connected else '❌ Desconectada'}")
            print(f"📷 Cámara: {'✅ Conectada' if controller.camera_connected else '❌ Desconectada'}")
            
        elif choice == '8':
            break
            
        else:
            print("❌ Opción no válida")
    
    # Desconectar al salir
    controller.disconnect_all()
    print("\n👋 ¡Hasta luego!")

if __name__ == "__main__":
    main()
