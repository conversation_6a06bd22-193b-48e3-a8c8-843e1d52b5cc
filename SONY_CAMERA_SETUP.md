# Configuración de Sony ZV-E10 para Control WiFi

## 📋 Requisitos Previos

- Sony ZV-E10 con firmware actualizado
- PC con Python 3.6 o superior
- Conexión WiFi disponible

## 🔧 Configuración en la Cámara

### Opción 1: Punto de Acceso de la Cámara (Recomendado)

1. **Enciende tu Sony ZV-E10**

2. **Accede al menú de configuración:**
   - Presiona el botón `MENU`
   - Navega a `Network` → `Wi-Fi Settings`

3. **Configura el control remoto:**
   - Selecciona `Ctrl w/ Smartphone`
   - Elige `On This Device`
   - La cámara creará su propio punto de acceso WiFi

4. **Anota la información de conexión:**
   - SSID (nombre de la red): Aparecerá en pantalla
   - Contraseña: También se mostrará en pantalla
   - Ejemplo: `DIRECT-xxxx:ZV-E10` con contraseña `xxxxxxxx`

5. **Conecta tu PC a la red de la cámara:**
   - Ve a configuración WiFi de tu PC
   - Busca la red con el nombre que anotaste
   - Conecta usando la contraseña proporcionada

### Opción 2: Red WiFi Compartida

1. **En la cámara:**
   - Ve a `Menu` → `Network` → `Wi-Fi Settings`
   - Selecciona `Ctrl w/ Smartphone`
   - Elige `Access Point Set.`
   - Selecciona tu red WiFi doméstica
   - Ingresa la contraseña de tu red

2. **Asegúrate de que tu PC esté en la misma red WiFi**

## 🚀 Uso del Software

### Instalación de Dependencias

```bash
# Instalar desde requirements.txt
pip install -r requirements.txt

# O instalar manualmente
pip install git+https://github.com/Bloodevil/sony_camera_api.git
```

### Prueba de Conexión

```bash
python test_sony_camera.py
```

Este script te dirá si la conexión es exitosa y mostrará las funciones disponibles.

### Control Completo

```bash
python sony_camera_control.py
```

Este script ofrece un menú interactivo con las siguientes opciones:
- Tomar fotografías
- Iniciar/detener Live View
- Ver estado de la cámara
- Listar funciones disponibles

## 📸 Funciones Disponibles

### Funciones Básicas
- `take_photo()`: Toma una fotografía
- `get_camera_status()`: Obtiene información del estado
- `start_liveview()`: Inicia transmisión de video en vivo
- `stop_liveview()`: Detiene la transmisión
- `get_available_functions()`: Lista todas las funciones disponibles

### Ejemplo de Uso Programático

```python
from sony_camera_control import SonyCameraController

# Crear controlador
controller = SonyCameraController()

# Conectar
if controller.discover_camera():
    # Tomar foto
    controller.take_photo()
    
    # Ver estado
    status = controller.get_camera_status()
    print(status)
    
    # Desconectar
    controller.disconnect()
```

## 🔍 Solución de Problemas

### Error de Conexión
- **Síntoma**: `Connection attempt failed`
- **Solución**: 
  1. Verifica que la cámara esté encendida
  2. Confirma que estás conectado a la red WiFi correcta
  3. Asegúrate de que el modo "Ctrl w/ Smartphone" esté activo

### Cámara No Responde
- **Síntoma**: La cámara no toma fotos
- **Solución**:
  1. Verifica que la cámara esté en modo "Remote Shooting"
  2. Asegúrate de que no esté en modo de reproducción
  3. Revisa que la batería tenga suficiente carga

### APIs No Disponibles
- **Síntoma**: Lista de APIs vacía o limitada
- **Solución**:
  1. Actualiza el firmware de la cámara
  2. Reinicia la conexión WiFi
  3. Verifica el modelo de cámara (ZV-E10 soportado)

## 📱 Integración con Otros Dispositivos

Si quieres controlar tanto la impresora Bambu Labs como la cámara Sony desde el mismo script, puedes modificar `main.py`:

```python
from sony_camera_control import SonyCameraController
import bambulabs_api as bl

# Configurar ambos dispositivos
printer = bl.Printer(IP, ACCESS_CODE, SERIAL)
camera = SonyCameraController()

# Usar ambos según necesites
if camera.discover_camera():
    camera.take_photo()
    
if printer.mqtt_start():
    printer.turn_light_on()
```

## 🔗 Referencias

- [Sony Camera Remote API Documentation](https://developer.sony.com/develop/cameras/)
- [Repositorio pysony](https://github.com/Bloodevil/sony_camera_api)
- [Manual Sony ZV-E10](https://www.sony.com/electronics/support/cameras-camcorders-digital-cameras/zv-e10)

## ⚠️ Notas Importantes

1. **Batería**: El WiFi consume batería rápidamente. Considera usar adaptador AC.
2. **Alcance**: El punto de acceso de la cámara tiene alcance limitado (~10 metros).
3. **Seguridad**: La conexión no está encriptada fuertemente. Úsala solo en redes confiables.
4. **Compatibilidad**: Algunas funciones pueden variar según la versión de firmware.
