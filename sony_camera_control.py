#!/usr/bin/env python3
"""
Control de Cámara Sony ZV-E10 via WiFi
Usando la librería pysony para acceder a la API remota de Sony
"""

import pysony
import time
import json
from typing import Optional, Dict, Any

class SonyCameraController:
    """Controlador para cámara Sony ZV-E10 via WiFi"""
    
    def __init__(self):
        self.camera = None
        self.connected = False
        self.camera_info = {}
        
    def discover_camera(self) -> bool:
        """
        Descubre y conecta automáticamente con la cámara Sony
        Returns: True si se conecta exitosamente, False en caso contrario
        """
        try:
            print("Buscando cámara Sony...")
            self.camera = pysony.SonyAPI()
            
            # Intentar obtener información básica para verificar conexión
            api_list = self.camera.getAvailableApiList()
            if api_list:
                self.connected = True
                print("✅ Cámara Sony conectada exitosamente!")
                print(f"APIs disponibles: {len(api_list)} funciones")
                return True
            else:
                print("❌ No se pudo conectar con la cámara")
                return False
                
        except Exception as e:
            print(f"❌ Error al conectar con la cámara: {e}")
            print("\n📋 Pasos para configurar WiFi en Sony ZV-E10:")
            print("1. Ve a Menu → Network → Wi-Fi Settings")
            print("2. Selecciona 'Ctrl w/ Smartphone'")
            print("3. Selecciona 'On This Device' o 'Access Point Set.'")
            print("4. Conecta tu PC a la red WiFi de la cámara")
            print("5. Ejecuta este script nuevamente")
            return False
    
    def get_camera_status(self) -> Optional[Dict[str, Any]]:
        """
        Obtiene el estado actual de la cámara
        Returns: Diccionario con información del estado o None si hay error
        """
        if not self.connected:
            print("❌ Cámara no conectada")
            return None
            
        try:
            # Obtener información básica
            status = {}
            
            # Intentar obtener diferentes tipos de información
            try:
                status['available_apis'] = self.camera.getAvailableApiList()
            except:
                status['available_apis'] = "No disponible"
                
            try:
                status['camera_function'] = self.camera.getCameraFunction()
            except:
                status['camera_function'] = "No disponible"
                
            try:
                status['shoot_mode'] = self.camera.getShootMode()
            except:
                status['shoot_mode'] = "No disponible"
                
            return status
            
        except Exception as e:
            print(f"❌ Error al obtener estado: {e}")
            return None
    
    def take_photo(self) -> bool:
        """
        Toma una fotografía
        Returns: True si la foto se tomó exitosamente, False en caso contrario
        """
        if not self.connected:
            print("❌ Cámara no conectada")
            return False
            
        try:
            print("📸 Tomando fotografía...")
            
            # Verificar que la cámara esté en modo foto
            try:
                current_mode = self.camera.getCameraFunction()
                if current_mode and 'Remote Shooting' not in str(current_mode):
                    print("⚠️  Cambiando a modo Remote Shooting...")
                    self.camera.setCameraFunction("Remote Shooting")
                    time.sleep(1)
            except:
                print("⚠️  No se pudo verificar/cambiar el modo de cámara")
            
            # Tomar la foto
            result = self.camera.actTakePicture()
            
            if result:
                print("✅ ¡Fotografía tomada exitosamente!")
                return True
            else:
                print("❌ Error al tomar la fotografía")
                return False
                
        except Exception as e:
            print(f"❌ Error al tomar fotografía: {e}")
            return False
    
    def start_liveview(self) -> Optional[str]:
        """
        Inicia el live view de la cámara
        Returns: URL del stream de video o None si hay error
        """
        if not self.connected:
            print("❌ Cámara no conectada")
            return None
            
        try:
            print("📹 Iniciando Live View...")
            liveview_url = self.camera.liveview()
            
            if liveview_url:
                print(f"✅ Live View iniciado: {liveview_url}")
                return liveview_url
            else:
                print("❌ No se pudo iniciar Live View")
                return None
                
        except Exception as e:
            print(f"❌ Error al iniciar Live View: {e}")
            return None
    
    def stop_liveview(self) -> bool:
        """
        Detiene el live view de la cámara
        Returns: True si se detuvo exitosamente, False en caso contrario
        """
        if not self.connected:
            print("❌ Cámara no conectada")
            return False
            
        try:
            print("⏹️  Deteniendo Live View...")
            result = self.camera.stopLiveview()
            
            if result:
                print("✅ Live View detenido")
                return True
            else:
                print("❌ Error al detener Live View")
                return False
                
        except Exception as e:
            print(f"❌ Error al detener Live View: {e}")
            return False
    
    def get_available_functions(self) -> Optional[list]:
        """
        Obtiene la lista de funciones disponibles en la cámara
        Returns: Lista de funciones disponibles o None si hay error
        """
        if not self.connected:
            print("❌ Cámara no conectada")
            return None
            
        try:
            functions = self.camera.getAvailableApiList()
            return functions
        except Exception as e:
            print(f"❌ Error al obtener funciones: {e}")
            return None
    
    def disconnect(self):
        """Desconecta de la cámara"""
        if self.connected:
            print("🔌 Desconectando de la cámara...")
            self.connected = False
            self.camera = None
            print("✅ Desconectado")

def main():
    """Función principal de demostración"""
    print("🎥 Control de Cámara Sony ZV-E10")
    print("=" * 40)
    
    # Crear controlador
    controller = SonyCameraController()
    
    # Intentar conectar
    if not controller.discover_camera():
        return
    
    try:
        # Mostrar estado
        print("\n📊 Estado de la cámara:")
        status = controller.get_camera_status()
        if status:
            print(json.dumps(status, indent=2, default=str))
        
        # Mostrar funciones disponibles
        print("\n🔧 Funciones disponibles:")
        functions = controller.get_available_functions()
        if functions:
            for i, func in enumerate(functions, 1):
                print(f"{i}. {func}")
        
        # Menú interactivo
        while True:
            print("\n" + "=" * 40)
            print("¿Qué deseas hacer?")
            print("1. Tomar fotografía")
            print("2. Iniciar Live View")
            print("3. Detener Live View")
            print("4. Ver estado de la cámara")
            print("5. Salir")
            
            choice = input("\nSelecciona una opción (1-5): ").strip()
            
            if choice == '1':
                controller.take_photo()
            elif choice == '2':
                url = controller.start_liveview()
                if url:
                    print(f"Live View URL: {url}")
            elif choice == '3':
                controller.stop_liveview()
            elif choice == '4':
                status = controller.get_camera_status()
                if status:
                    print(json.dumps(status, indent=2, default=str))
            elif choice == '5':
                break
            else:
                print("❌ Opción no válida")
    
    except KeyboardInterrupt:
        print("\n\n⏹️  Interrumpido por el usuario")
    
    finally:
        # Desconectar
        controller.disconnect()

if __name__ == "__main__":
    main()
